import { useState, useEffect } from 'react'

export interface Strategy {
  id: string
  name: string
  description?: string
  type: 'ARBITRAGE' | 'TREND' | 'MARKET_MAKER' | 'HIGH_FREQUENCY' | 'AI' | 'GRID' | 'DCA' | 'CUSTOM'
  status: 'RUNNING' | 'STOPPED' | 'PAUSED' | 'ERROR' | 'BACKTESTING'
  exchanges: string[]
  symbols: string[]
  parameters: Record<string, any>
  totalReturn: number
  dailyReturn: number
  maxDrawdown: number
  sharpeRatio: number
  winRate: number
  totalTrades: number
  maxPosition?: number
  stopLoss?: number
  takeProfit?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export function Strategies() {
  const [strategies, setStrategies] = useState<Strategy[]>([])
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [createError, setCreateError] = useState<string | null>(null)
  const [filter, setFilter] = useState<{
    status?: string
    type?: string
  }>({})

  // 策略创建表单数据
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'TREND' as Strategy['type'],
    language: 'typescript' as 'typescript' | 'python',
    code: '',
    exchanges: ['binance'],
    symbols: ['BTC/USDT'],
    parameters: {},
    enabled: false,
    riskLimits: {
      maxPositionSize: 1000,
      maxDailyLoss: 100,
      maxDrawdown: 0.1
    }
  })

  // 从API获取真实策略数据
  const fetchStrategies = async () => {
    try {
      setIsLoading(true)

      // 调用真实的API获取策略数据
      const token = localStorage.getItem('accessToken')
      if (!token) {
        setStrategies([])
        setIsLoading(false)
        return
      }

      const response = await fetch('http://localhost:3001/api/v1/strategies', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setStrategies(data.strategies || [])
      } else {
        console.error('Failed to fetch strategies:', response.statusText)
        setStrategies([])
      }

      setIsLoading(false)
    } catch (error) {
      console.error('Failed to fetch strategies:', error)
      setStrategies([])
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchStrategies()
  }, [])

  const filteredStrategies = strategies.filter(strategy => {
    if (filter.status && strategy.status !== filter.status) return false
    if (filter.type && strategy.type !== filter.type) return false
    return true
  })

  // 处理表单输入变化
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    // 清除错误信息
    if (createError) {
      setCreateError(null)
    }
  }

  // 创建策略
  const handleCreateStrategy = async () => {
    try {
      setIsCreating(true)
      setCreateError(null)

      // 验证必填字段
      if (!formData.name.trim()) {
        throw new Error('策略名称不能为空')
      }
      if (!formData.code.trim()) {
        throw new Error('策略代码不能为空')
      }

      const token = localStorage.getItem('accessToken')
      if (!token) {
        throw new Error('请先登录')
      }

      // 准备API请求数据，映射前端类型到后端期望的格式
      const typeMapping: Record<string, string> = {
        'ARBITRAGE': 'arbitrage',
        'TREND': 'trend',
        'MARKET_MAKER': 'market_maker',
        'HIGH_FREQUENCY': 'high_frequency',
        'AI': 'ai_ml',
        'GRID': 'trend' // 暂时映射为trend，后续可以扩展
      }

      const requestData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        type: typeMapping[formData.type] || 'trend',
        language: formData.language,
        code: formData.code,
        exchanges: formData.exchanges,
        symbols: formData.symbols,
        parameters: formData.parameters,
        enabled: formData.enabled,
        riskLimits: formData.riskLimits
      }

      const response = await fetch('http://localhost:3001/api/v1/strategies', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '创建策略失败')
      }

      const result = await response.json()

      // 重新获取策略列表
      await fetchStrategies()

      // 关闭模态框并重置表单
      setShowCreateModal(false)
      setFormData({
        name: '',
        description: '',
        type: 'TREND',
        language: 'typescript',
        code: '',
        exchanges: ['binance'],
        symbols: ['BTC/USDT'],
        parameters: {},
        enabled: false,
        riskLimits: {
          maxPositionSize: 1000,
          maxDailyLoss: 100,
          maxDrawdown: 0.1
        }
      })

    } catch (error) {
      setCreateError(error instanceof Error ? error.message : '创建策略失败')
    } finally {
      setIsCreating(false)
    }
  }

  const handleStrategyAction = (strategyId: string, action: 'start' | 'stop' | 'pause' | 'delete') => {
    setStrategies(prev => prev.map(strategy => {
      if (strategy.id === strategyId) {
        switch (action) {
          case 'start':
            return { ...strategy, status: 'RUNNING' as const }
          case 'stop':
            return { ...strategy, status: 'STOPPED' as const }
          case 'pause':
            return { ...strategy, status: 'PAUSED' as const }
          case 'delete':
            return { ...strategy, isActive: false }
          default:
            return strategy
        }
      }
      return strategy
    }).filter(strategy => strategy.isActive))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'RUNNING': return '#10b981'
      case 'STOPPED': return '#6b7280'
      case 'PAUSED': return '#f59e0b'
      case 'ERROR': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'RUNNING': return '运行中'
      case 'STOPPED': return '已停止'
      case 'PAUSED': return '已暂停'
      case 'ERROR': return '错误'
      default: return status
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case 'ARBITRAGE': return '套利'
      case 'TREND': return '趋势'
      case 'MARKET_MAKER': return '做市'
      case 'GRID': return '网格'
      case 'AI': return 'AI策略'
      default: return type
    }
  }

  return (
    <div style={{ padding: '20px' }}>
      {/* 页面标题和操作 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px'
      }}>
        <div>
          <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#333', margin: '0 0 8px' }}>
            策略管理
          </h1>
          <p style={{ color: '#666', margin: 0 }}>
            创建、管理和监控您的量化交易策略
          </p>
        </div>

        <button
          onClick={() => setShowCreateModal(true)}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '12px 24px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <span style={{ fontSize: '16px' }}>+</span>
          创建策略
        </button>
      </div>

      {/* 过滤器 */}
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        marginBottom: '20px',
        display: 'flex',
        gap: '16px',
        alignItems: 'center'
      }}>
        <div>
          <label style={{ fontSize: '14px', fontWeight: '500', color: '#333', marginRight: '8px' }}>
            状态:
          </label>
          <select
            value={filter.status || ''}
            onChange={(e) => setFilter(prev => ({ ...prev, status: e.target.value || undefined }))}
            style={{
              padding: '6px 12px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              fontSize: '14px'
            }}
          >
            <option value="">全部</option>
            <option value="RUNNING">运行中</option>
            <option value="STOPPED">已停止</option>
            <option value="PAUSED">已暂停</option>
            <option value="ERROR">错误</option>
          </select>
        </div>

        <div>
          <label style={{ fontSize: '14px', fontWeight: '500', color: '#333', marginRight: '8px' }}>
            类型:
          </label>
          <select
            value={filter.type || ''}
            onChange={(e) => setFilter(prev => ({ ...prev, type: e.target.value || undefined }))}
            style={{
              padding: '6px 12px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              fontSize: '14px'
            }}
          >
            <option value="">全部</option>
            <option value="ARBITRAGE">套利</option>
            <option value="TREND">趋势</option>
            <option value="MARKET_MAKER">做市</option>
            <option value="GRID">网格</option>
            <option value="AI">AI策略</option>
          </select>
        </div>

        <div style={{ marginLeft: 'auto', color: '#666', fontSize: '14px' }}>
          共 {filteredStrategies.length} 个策略
        </div>
      </div>

      {/* 策略列表 */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        {isLoading ? (
          <div style={{ padding: '60px', textAlign: 'center' }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '4px solid #f3f4f6',
              borderTop: '4px solid #3b82f6',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 16px'
            }} />
            <p style={{ color: '#666' }}>加载策略列表...</p>
          </div>
        ) : filteredStrategies.length === 0 ? (
          <div style={{ padding: '60px', textAlign: 'center' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
            <h3 style={{ fontSize: '18px', fontWeight: '500', color: '#333', margin: '0 0 8px' }}>
              暂无策略
            </h3>
            <p style={{ color: '#666', marginBottom: '24px' }}>
              开始创建您的第一个量化交易策略
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              style={{
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                padding: '10px 20px',
                fontSize: '14px',
                cursor: 'pointer'
              }}
            >
              创建策略
            </button>
          </div>
        ) : (
          <div>
            {/* 表头 */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr 1fr 120px',
              gap: '16px',
              padding: '16px 20px',
              backgroundColor: '#f9fafb',
              borderBottom: '1px solid #e5e7eb',
              fontSize: '12px',
              fontWeight: '500',
              color: '#6b7280',
              textTransform: 'uppercase'
            }}>
              <div>策略名称</div>
              <div>类型</div>
              <div>状态</div>
              <div>总收益率</div>
              <div>日收益率</div>
              <div>交易次数</div>
              <div>操作</div>
            </div>

            {/* 策略行 */}
            {filteredStrategies.map((strategy) => (
              <div
                key={strategy.id}
                style={{
                  display: 'grid',
                  gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr 1fr 120px',
                  gap: '16px',
                  padding: '16px 20px',
                  borderBottom: '1px solid #e5e7eb',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                onClick={() => setSelectedStrategy(strategy)}
              >
                {/* 策略名称和描述 */}
                <div>
                  <div style={{ fontWeight: '500', color: '#333', marginBottom: '4px' }}>
                    {strategy.name}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {strategy.description || '暂无描述'}
                  </div>
                  <div style={{ fontSize: '11px', color: '#9ca3af', marginTop: '2px' }}>
                    {strategy.symbols.join(', ')}
                  </div>
                </div>

                {/* 类型 */}
                <div style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  padding: '4px 8px',
                  backgroundColor: '#f3f4f6',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#374151',
                  width: 'fit-content'
                }}>
                  {getTypeText(strategy.type)}
                </div>

                {/* 状态 */}
                <div style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '6px',
                  width: 'fit-content'
                }}>
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    backgroundColor: getStatusColor(strategy.status)
                  }} />
                  <span style={{
                    fontSize: '12px',
                    fontWeight: '500',
                    color: getStatusColor(strategy.status)
                  }}>
                    {getStatusText(strategy.status)}
                  </span>
                </div>

                {/* 总收益率 */}
                <div style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: strategy.totalReturn >= 0 ? '#10b981' : '#ef4444'
                }}>
                  {strategy.totalReturn >= 0 ? '+' : ''}{strategy.totalReturn.toFixed(2)}%
                </div>

                {/* 日收益率 */}
                <div style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: strategy.dailyReturn >= 0 ? '#10b981' : '#ef4444'
                }}>
                  {strategy.dailyReturn >= 0 ? '+' : ''}{strategy.dailyReturn.toFixed(2)}%
                </div>

                {/* 交易次数 */}
                <div style={{ fontSize: '14px', color: '#333' }}>
                  {strategy.totalTrades}
                </div>

                {/* 操作按钮 */}
                <div style={{ display: 'flex', gap: '4px' }} onClick={(e) => e.stopPropagation()}>
                  {strategy.status === 'STOPPED' ? (
                    <button
                      onClick={() => handleStrategyAction(strategy.id, 'start')}
                      style={{
                        padding: '4px 8px',
                        backgroundColor: '#10b981',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        fontSize: '11px',
                        cursor: 'pointer'
                      }}
                      title="启动策略"
                    >
                      启动
                    </button>
                  ) : strategy.status === 'RUNNING' ? (
                    <button
                      onClick={() => handleStrategyAction(strategy.id, 'stop')}
                      style={{
                        padding: '4px 8px',
                        backgroundColor: '#ef4444',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        fontSize: '11px',
                        cursor: 'pointer'
                      }}
                      title="停止策略"
                    >
                      停止
                    </button>
                  ) : null}

                  <button
                    onClick={() => handleStrategyAction(strategy.id, 'delete')}
                    style={{
                      padding: '4px 8px',
                      backgroundColor: '#6b7280',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      fontSize: '11px',
                      cursor: 'pointer'
                    }}
                    title="删除策略"
                  >
                    删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 创建策略模态框 */}
      {showCreateModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: '24px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h2 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '20px' }}>
              创建新策略
            </h2>

            {/* 错误提示 */}
            {createError && (
              <div style={{
                backgroundColor: '#fef2f2',
                border: '1px solid #fecaca',
                borderRadius: '6px',
                padding: '12px',
                marginBottom: '16px',
                color: '#dc2626',
                fontSize: '14px'
              }}>
                {createError}
              </div>
            )}

            {/* 策略基本信息 */}
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px', color: '#333' }}>
                基本信息
              </h3>

              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#333', marginBottom: '6px' }}>
                  策略名称 *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleFormChange('name', e.target.value)}
                  placeholder="请输入策略名称"
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#333', marginBottom: '6px' }}>
                  策略描述
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleFormChange('description', e.target.value)}
                  placeholder="请输入策略描述"
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    resize: 'vertical',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
                <div>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#333', marginBottom: '6px' }}>
                    策略类型 *
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => handleFormChange('type', e.target.value)}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      outline: 'none',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="ARBITRAGE">套利策略</option>
                    <option value="TREND">趋势策略</option>
                    <option value="MARKET_MAKER">做市策略</option>
                    <option value="HIGH_FREQUENCY">高频策略</option>
                    <option value="AI">AI策略</option>
                    <option value="GRID">网格策略</option>
                  </select>
                </div>

                <div>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#333', marginBottom: '6px' }}>
                    编程语言 *
                  </label>
                  <select
                    value={formData.language}
                    onChange={(e) => handleFormChange('language', e.target.value)}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      outline: 'none',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="typescript">TypeScript</option>
                    <option value="python">Python</option>
                  </select>
                </div>
              </div>
            </div>

            {/* 策略代码 */}
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px', color: '#333' }}>
                策略代码
              </h3>

              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#333', marginBottom: '6px' }}>
                  策略代码 *
                </label>
                <textarea
                  value={formData.code}
                  onChange={(e) => handleFormChange('code', e.target.value)}
                  placeholder={formData.language === 'typescript'
                    ? `// TypeScript 策略代码示例
export function strategy(context: StrategyContext) {
  const { marketData, indicators, portfolio } = context

  // 在这里编写您的策略逻辑
  const signal = {
    action: 'HOLD',
    quantity: 0,
    price: marketData.close,
    confidence: 0.5,
    reason: '示例策略'
  }

  return { signals: [signal] }
}`
                    : `# Python 策略代码示例
def strategy(context):
    market_data = context['market_data']
    indicators = context['indicators']
    portfolio = context['portfolio']

    # 在这里编写您的策略逻辑
    signal = {
        'action': 'HOLD',
        'quantity': 0,
        'price': market_data['close'],
        'confidence': 0.5,
        'reason': '示例策略'
    }

    return {'signals': [signal]}
`}
                  rows={12}
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '13px',
                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                    outline: 'none',
                    resize: 'vertical',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            </div>

            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px' }}>
              <button
                onClick={() => setShowCreateModal(false)}
                disabled={isCreating}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#6b7280',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: isCreating ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  opacity: isCreating ? 0.6 : 1
                }}
              >
                取消
              </button>
              <button
                onClick={handleCreateStrategy}
                disabled={isCreating || !formData.name.trim() || !formData.code.trim()}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: (isCreating || !formData.name.trim() || !formData.code.trim()) ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  opacity: (isCreating || !formData.name.trim() || !formData.code.trim()) ? 0.6 : 1,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                {isCreating && (
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid #ffffff',
                    borderTop: '2px solid transparent',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }} />
                )}
                {isCreating ? '创建中...' : '创建策略'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* CSS动画 */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
