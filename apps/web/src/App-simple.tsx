import { useState, useEffect } from 'react'
import { Routes, Route, Navigate, Link, useLocation } from 'react-router-dom'

interface MarketData {
  symbol: string
  timestamp: number
  bid: number
  ask: number
  last: number
  volume: number
  high: number
  low: number
  open: number
  close: number
  change?: number
  changePercent?: number
}

interface WebSocketMessage {
  type: string
  clientId?: string
  symbol?: string
  data?: any
  timestamp: number
}

// 简单的认证状态管理
const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    return localStorage.getItem('isAuthenticated') === 'true'
  })

  const login = (email?: string) => {
    setIsAuthenticated(true)
    localStorage.setItem('isAuthenticated', 'true')

    // 生成一个简单的模拟token（在实际应用中应该从服务器获取）
    const mockToken = `mock_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    localStorage.setItem('accessToken', mockToken)

    // 也可以保存用户信息
    if (email) {
      localStorage.setItem('userEmail', email)
    }
  }

  const logout = () => {
    setIsAuthenticated(false)
    localStorage.removeItem('isAuthenticated')
    localStorage.removeItem('accessToken')
    localStorage.removeItem('userEmail')
  }

  return { isAuthenticated, login, logout }
}

// 简化的登录组件
function LoginForm() {
  const { login } = useAuth()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // 简单验证
    if (email && password) {
      login(email)
      // 强制刷新页面以触发路由重定向
      setTimeout(() => {
        window.location.href = '/'
      }, 100)
    }
  }

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#f5f5f5'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '40px',
        borderRadius: '12px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        width: '100%',
        maxWidth: '400px'
      }}>
        <div style={{ textAlign: 'center', marginBottom: '30px' }}>
          <div style={{
            width: '60px',
            height: '60px',
            backgroundColor: '#3b82f6',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 20px'
          }}>
            <span style={{ color: 'white', fontSize: '24px', fontWeight: 'bold' }}>SF</span>
          </div>
          <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#333', margin: '0 0 8px' }}>
            欢迎回来
          </h1>
          <p style={{ color: '#666', margin: 0 }}>
            登录到您的SFQuant账户
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: '20px' }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#333',
              marginBottom: '8px'
            }}>
              邮箱地址
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              style={{
                width: '100%',
                padding: '12px',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '14px',
                outline: 'none',
                boxSizing: 'border-box'
              }}
              placeholder="请输入您的邮箱"
            />
          </div>

          <div style={{ marginBottom: '24px' }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#333',
              marginBottom: '8px'
            }}>
              密码
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              style={{
                width: '100%',
                padding: '12px',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '14px',
                outline: 'none',
                boxSizing: 'border-box'
              }}
              placeholder="请输入您的密码"
            />
          </div>

          <button
            type="submit"
            style={{
              width: '100%',
              padding: '12px',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: '500',
              cursor: 'pointer'
            }}
          >
            登录
          </button>
        </form>

        <div style={{
          textAlign: 'center',
          marginTop: '24px',
          paddingTop: '24px',
          borderTop: '1px solid #e5e7eb'
        }}>
          <p style={{ color: '#666', fontSize: '14px', margin: 0 }}>
            还没有账户？{' '}
            <Link
              to="/register"
              style={{
                color: '#3b82f6',
                textDecoration: 'none',
                fontWeight: '500'
              }}
            >
              立即注册
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

// 简化的注册组件
function RegisterForm() {
  const { login } = useAuth()
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: ''
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // 简单验证
    if (formData.email && formData.password && formData.name) {
      login(formData.email)
      // 强制刷新页面以触发路由重定向
      setTimeout(() => {
        window.location.href = '/'
      }, 100)
    }
  }

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#f5f5f5'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '40px',
        borderRadius: '12px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        width: '100%',
        maxWidth: '400px'
      }}>
        <div style={{ textAlign: 'center', marginBottom: '30px' }}>
          <div style={{
            width: '60px',
            height: '60px',
            backgroundColor: '#3b82f6',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 20px'
          }}>
            <span style={{ color: 'white', fontSize: '24px', fontWeight: 'bold' }}>SF</span>
          </div>
          <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#333', margin: '0 0 8px' }}>
            创建账户
          </h1>
          <p style={{ color: '#666', margin: 0 }}>
            加入SFQuant量化交易平台
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: '16px' }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#333',
              marginBottom: '6px'
            }}>
              姓名
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              style={{
                width: '100%',
                padding: '10px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                outline: 'none',
                boxSizing: 'border-box'
              }}
              placeholder="请输入您的姓名"
            />
          </div>

          <div style={{ marginBottom: '16px' }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#333',
              marginBottom: '6px'
            }}>
              邮箱地址
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              style={{
                width: '100%',
                padding: '10px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                outline: 'none',
                boxSizing: 'border-box'
              }}
              placeholder="请输入您的邮箱"
            />
          </div>

          <div style={{ marginBottom: '24px' }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#333',
              marginBottom: '6px'
            }}>
              密码
            </label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              required
              style={{
                width: '100%',
                padding: '10px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                outline: 'none',
                boxSizing: 'border-box'
              }}
              placeholder="至少6个字符"
            />
          </div>

          <button
            type="submit"
            style={{
              width: '100%',
              padding: '12px',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: '500',
              cursor: 'pointer'
            }}
          >
            创建账户
          </button>
        </form>

        <div style={{
          textAlign: 'center',
          marginTop: '24px',
          paddingTop: '24px',
          borderTop: '1px solid #e5e7eb'
        }}>
          <p style={{ color: '#666', fontSize: '14px', margin: 0 }}>
            已有账户？{' '}
            <Link
              to="/login"
              style={{
                color: '#3b82f6',
                textDecoration: 'none',
                fontWeight: '500'
              }}
            >
              立即登录
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

// 策略管理组件
function Strategies() {
  const [strategies, setStrategies] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [createError, setCreateError] = useState<string | null>(null)

  // 策略创建表单数据
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'TREND',
    language: 'typescript',
    code: '',
    exchanges: ['binance'],
    symbols: ['BTC/USDT'],
    parameters: {},
    enabled: false,
    riskLimits: {
      maxPositionSize: 1000,
      maxDailyLoss: 100,
      maxDrawdown: 0.1
    }
  })

  useEffect(() => {
    const fetchStrategies = async () => {
      try {
        setIsLoading(true)

        // 调用真实的API获取策略数据
        const token = localStorage.getItem('accessToken')
        if (!token) {
          setStrategies([])
          setIsLoading(false)
          return
        }

        const response = await fetch('http://localhost:3001/api/v1/strategies', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.ok) {
          const data = await response.json()
          setStrategies(data.data?.strategies || [])
        } else {
          console.error('Failed to fetch strategies:', response.statusText)
          setStrategies([])
        }

        setIsLoading(false)
      } catch (error) {
        console.error('Failed to fetch strategies:', error)
        setStrategies([])
        setIsLoading(false)
      }
    }

    fetchStrategies()
  }, [])

  // 处理表单输入变化
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    // 清除错误信息
    if (createError) {
      setCreateError(null)
    }
  }

  // 创建策略
  const handleCreateStrategy = async () => {
    try {
      setIsCreating(true)
      setCreateError(null)

      // 验证必填字段
      if (!formData.name.trim()) {
        throw new Error('策略名称不能为空')
      }
      if (!formData.code.trim()) {
        throw new Error('策略代码不能为空')
      }

      const token = localStorage.getItem('accessToken')
      if (!token) {
        throw new Error('请先登录')
      }

      // 准备API请求数据，映射前端类型到后端期望的格式
      const typeMapping: Record<string, string> = {
        'ARBITRAGE': 'arbitrage',
        'TREND': 'trend',
        'MARKET_MAKER': 'market_maker',
        'HIGH_FREQUENCY': 'high_frequency',
        'AI': 'ai_ml',
        'GRID': 'trend' // 暂时映射为trend，后续可以扩展
      }

      const requestData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        type: typeMapping[formData.type] || 'trend',
        language: formData.language,
        code: formData.code,
        exchanges: formData.exchanges,
        symbols: formData.symbols,
        parameters: formData.parameters,
        enabled: formData.enabled,
        riskLimits: formData.riskLimits
      }

      const response = await fetch('/api/v1/strategies', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '创建策略失败')
      }

      const result = await response.json()

      // 重新获取策略列表
      await fetchStrategies()

      // 关闭模态框并重置表单
      setShowCreateModal(false)
      setFormData({
        name: '',
        description: '',
        type: 'TREND',
        language: 'typescript',
        code: '',
        exchanges: ['binance'],
        symbols: ['BTC/USDT'],
        parameters: {},
        enabled: false,
        riskLimits: {
          maxPositionSize: 1000,
          maxDailyLoss: 100,
          maxDrawdown: 0.1
        }
      })

    } catch (error) {
      setCreateError(error instanceof Error ? error.message : '创建策略失败')
    } finally {
      setIsCreating(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'RUNNING': return '#10b981'
      case 'STOPPED': return '#6b7280'
      default: return '#6b7280'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'RUNNING': return '运行中'
      case 'STOPPED': return '已停止'
      default: return status
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case 'ARBITRAGE': return '套利'
      case 'TREND': return '趋势'
      case 'GRID': return '网格'
      default: return type
    }
  }

  return (
    <div style={{ padding: '20px' }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px'
      }}>
        <div>
          <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#333', margin: '0 0 8px' }}>
            策略管理
          </h1>
          <p style={{ color: '#666', margin: 0 }}>
            创建、管理和监控您的量化交易策略
          </p>
        </div>

        <button
          onClick={() => setShowCreateModal(true)}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '12px 24px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <span style={{ fontSize: '16px' }}>+</span>
          创建策略
        </button>
      </div>

      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr 1fr',
          gap: '16px',
          padding: '16px 20px',
          backgroundColor: '#f9fafb',
          borderBottom: '1px solid #e5e7eb',
          fontSize: '12px',
          fontWeight: '500',
          color: '#6b7280',
          textTransform: 'uppercase'
        }}>
          <div>策略名称</div>
          <div>类型</div>
          <div>状态</div>
          <div>总收益率</div>
          <div>日收益率</div>
          <div>交易次数</div>
        </div>

        {isLoading ? (
          <div style={{ padding: '60px', textAlign: 'center' }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '4px solid #f3f4f6',
              borderTop: '4px solid #3b82f6',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 16px'
            }} />
            <p style={{ color: '#666' }}>加载策略列表...</p>
          </div>
        ) : strategies.length === 0 ? (
          <div style={{ padding: '60px', textAlign: 'center' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
            <h3 style={{ fontSize: '18px', fontWeight: '500', color: '#333', margin: '0 0 8px' }}>
              暂无策略
            </h3>
            <p style={{ color: '#666', marginBottom: '24px' }}>
              开始创建您的第一个量化交易策略
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              style={{
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                padding: '10px 20px',
                fontSize: '14px',
                cursor: 'pointer'
              }}
            >
              创建策略
            </button>
          </div>
        ) : (
          strategies.map((strategy) => (
            <div
              key={strategy.id}
              style={{
                display: 'grid',
                gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr 1fr',
                gap: '16px',
                padding: '16px 20px',
                borderBottom: '1px solid #e5e7eb',
                cursor: 'pointer'
              }}
            >
              <div>
                <div style={{ fontWeight: '500', color: '#333', marginBottom: '4px' }}>
                  {strategy.name}
                </div>
              </div>

              <div style={{
                display: 'inline-flex',
                alignItems: 'center',
                padding: '4px 8px',
                backgroundColor: '#f3f4f6',
                borderRadius: '4px',
                fontSize: '12px',
                fontWeight: '500',
                color: '#374151',
                width: 'fit-content'
              }}>
                {getTypeText(strategy.type)}
              </div>

              <div style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: '6px',
                width: 'fit-content'
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  backgroundColor: getStatusColor(strategy.status)
                }} />
                <span style={{
                  fontSize: '12px',
                  fontWeight: '500',
                  color: getStatusColor(strategy.status)
                }}>
                  {getStatusText(strategy.status)}
                </span>
              </div>

              <div style={{
                fontSize: '14px',
                fontWeight: '500',
                color: strategy.totalReturn >= 0 ? '#10b981' : '#ef4444'
              }}>
                {strategy.totalReturn >= 0 ? '+' : ''}{strategy.totalReturn?.toFixed(2) || '0.00'}%
              </div>

              <div style={{
                fontSize: '14px',
                fontWeight: '500',
                color: strategy.dailyReturn >= 0 ? '#10b981' : '#ef4444'
              }}>
                {strategy.dailyReturn >= 0 ? '+' : ''}{strategy.dailyReturn?.toFixed(2) || '0.00'}%
              </div>

              <div style={{ fontSize: '14px', color: '#333' }}>
                {strategy.totalTrades || 0}
              </div>
            </div>
          ))
        )}
      </div>

      {/* 创建策略模态框 */}
      {showCreateModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: '24px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h2 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '20px' }}>
              创建新策略
            </h2>

            {/* 错误提示 */}
            {createError && (
              <div style={{
                backgroundColor: '#fef2f2',
                border: '1px solid #fecaca',
                borderRadius: '6px',
                padding: '12px',
                marginBottom: '16px',
                color: '#dc2626',
                fontSize: '14px'
              }}>
                {createError}
              </div>
            )}

            {/* 策略基本信息 */}
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px', color: '#333' }}>
                基本信息
              </h3>

              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#333', marginBottom: '6px' }}>
                  策略名称 *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleFormChange('name', e.target.value)}
                  placeholder="请输入策略名称"
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#333', marginBottom: '6px' }}>
                  策略描述
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleFormChange('description', e.target.value)}
                  placeholder="请输入策略描述"
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    resize: 'vertical',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
                <div>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#333', marginBottom: '6px' }}>
                    策略类型 *
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => handleFormChange('type', e.target.value)}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      outline: 'none',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="ARBITRAGE">套利策略</option>
                    <option value="TREND">趋势策略</option>
                    <option value="MARKET_MAKER">做市策略</option>
                    <option value="HIGH_FREQUENCY">高频策略</option>
                    <option value="AI">AI策略</option>
                    <option value="GRID">网格策略</option>
                  </select>
                </div>

                <div>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#333', marginBottom: '6px' }}>
                    编程语言 *
                  </label>
                  <select
                    value={formData.language}
                    onChange={(e) => handleFormChange('language', e.target.value)}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      outline: 'none',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="typescript">TypeScript</option>
                    <option value="python">Python</option>
                  </select>
                </div>
              </div>
            </div>

            {/* 策略代码 */}
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px', color: '#333' }}>
                策略代码
              </h3>

              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#333', marginBottom: '6px' }}>
                  策略代码 *
                </label>
                <textarea
                  value={formData.code}
                  onChange={(e) => handleFormChange('code', e.target.value)}
                  placeholder={formData.language === 'typescript'
                    ? `// TypeScript 策略代码示例
export function strategy(context: StrategyContext) {
  const { marketData, indicators, portfolio } = context

  // 在这里编写您的策略逻辑
  const signal = {
    action: 'HOLD',
    quantity: 0,
    price: marketData.close,
    confidence: 0.5,
    reason: '示例策略'
  }

  return { signals: [signal] }
}`
                    : `# Python 策略代码示例
def strategy(context):
    market_data = context['market_data']
    indicators = context['indicators']
    portfolio = context['portfolio']

    # 在这里编写您的策略逻辑
    signal = {
        'action': 'HOLD',
        'quantity': 0,
        'price': market_data['close'],
        'confidence': 0.5,
        'reason': '示例策略'
    }

    return {'signals': [signal]}
`}
                  rows={12}
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '13px',
                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                    outline: 'none',
                    resize: 'vertical',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            </div>

            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px' }}>
              <button
                onClick={() => setShowCreateModal(false)}
                disabled={isCreating}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#6b7280',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: isCreating ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  opacity: isCreating ? 0.6 : 1
                }}
              >
                取消
              </button>
              <button
                onClick={handleCreateStrategy}
                disabled={isCreating || !formData.name.trim() || !formData.code.trim()}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: (isCreating || !formData.name.trim() || !formData.code.trim()) ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  opacity: (isCreating || !formData.name.trim() || !formData.code.trim()) ? 0.6 : 1,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                {isCreating && (
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid #ffffff',
                    borderTop: '2px solid transparent',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }} />
                )}
                {isCreating ? '创建中...' : '创建策略'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// 导航组件
function Navigation({ onLogout }: { onLogout: () => void }) {
  const location = useLocation()

  const navItems = [
    { path: '/dashboard', label: '仪表板', icon: '📊' },
    { path: '/market-data', label: '市场数据', icon: '📈' },
    { path: '/strategies', label: '策略管理', icon: '🤖' },
    { path: '/exchanges', label: '交易所', icon: '🏦' },
    { path: '/settings', label: '设置', icon: '⚙️' }
  ]

  return (
    <nav style={{
      backgroundColor: 'white',
      borderBottom: '1px solid #e5e7eb',
      padding: '0 20px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      height: '60px'
    }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '30px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div style={{
            width: '32px',
            height: '32px',
            backgroundColor: '#3b82f6',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <span style={{ color: 'white', fontWeight: 'bold', fontSize: '14px' }}>SF</span>
          </div>
          <span style={{ fontWeight: 'bold', fontSize: '18px', color: '#333' }}>SFQuant</span>
        </div>

        <div style={{ display: 'flex', gap: '20px' }}>
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                borderRadius: '6px',
                textDecoration: 'none',
                color: location.pathname === item.path ? '#3b82f6' : '#666',
                backgroundColor: location.pathname === item.path ? '#eff6ff' : 'transparent',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              <span>{item.icon}</span>
              {item.label}
            </Link>
          ))}
        </div>
      </div>

      <button
        onClick={onLogout}
        style={{
          padding: '8px 16px',
          backgroundColor: '#ef4444',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          fontSize: '14px',
          cursor: 'pointer'
        }}
      >
        退出登录
      </button>
    </nav>
  )
}

// 仪表板组件
function Dashboard() {
  const [isConnected, setIsConnected] = useState(false)
  const [marketData, setMarketData] = useState<Record<string, MarketData>>({})
  const [messages, setMessages] = useState<WebSocketMessage[]>([])
  const [ws, setWs] = useState<WebSocket | null>(null)

  useEffect(() => {
    // 连接WebSocket
    const websocket = new WebSocket('ws://localhost:3001/ws')

    websocket.onopen = () => {
      console.log('WebSocket connected')
      setIsConnected(true)
      setWs(websocket)

      // 订阅一些交易对
      const symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']
      symbols.forEach(symbol => {
        websocket.send(JSON.stringify({
          type: 'subscribe',
          payload: { channel: 'price', symbol }
        }))
      })
    }

    websocket.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data)
        setMessages(prev => [message, ...prev.slice(0, 49)]) // 保留最近50条消息

        if (message.type === 'price_update' && message.symbol && message.data) {
          setMarketData(prev => ({
            ...prev,
            [message.symbol!]: message.data
          }))
        }
      } catch (error) {
        console.error('Failed to parse message:', error)
      }
    }

    websocket.onclose = () => {
      console.log('WebSocket disconnected')
      setIsConnected(false)
    }

    websocket.onerror = (error) => {
      console.error('WebSocket error:', error)
    }

    return () => {
      websocket.close()
    }
  }, [])

  const formatPrice = (price: number) => {
    if (price >= 1000) return price.toFixed(2)
    if (price >= 1) return price.toFixed(4)
    return price.toFixed(6)
  }

  const formatVolume = (volume: number) => {
    if (volume >= 1e9) return `${(volume / 1e9).toFixed(1)}B`
    if (volume >= 1e6) return `${(volume / 1e6).toFixed(1)}M`
    if (volume >= 1e3) return `${(volume / 1e3).toFixed(1)}K`
    return volume.toFixed(0)
  }

  return (
    <div style={{ fontFamily: 'Arial, sans-serif', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* 标题 */}
      <div style={{ padding: '20px', marginBottom: '30px', textAlign: 'center' }}>
        <h1 style={{ color: '#333', marginBottom: '10px' }}>🚀 SFQuant 量化交易系统</h1>
        <div style={{
          display: 'inline-flex',
          alignItems: 'center',
          padding: '8px 16px',
          borderRadius: '20px',
          backgroundColor: isConnected ? '#d4edda' : '#f8d7da',
          color: isConnected ? '#155724' : '#721c24',
          border: `1px solid ${isConnected ? '#c3e6cb' : '#f5c6cb'}`
        }}>
          <div style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: isConnected ? '#28a745' : '#dc3545',
            marginRight: '8px',
            animation: isConnected ? 'pulse 2s infinite' : 'none'
          }} />
          {isConnected ? '实时连接' : '连接断开'}
        </div>
      </div>

      <div style={{ padding: '0 20px' }}>
        {/* 市场数据表格 */}
        <div style={{ marginBottom: '30px' }}>
          <h2 style={{ color: '#333', marginBottom: '15px' }}>📊 实时市场数据</h2>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            overflow: 'hidden'
          }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ backgroundColor: '#f8f9fa' }}>
                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>交易对</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>价格</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>24h变化</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>成交量</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>更新时间</th>
                </tr>
              </thead>
              <tbody>
                {Object.values(marketData).map((data) => (
                  <tr key={data.symbol} style={{ borderBottom: '1px solid #dee2e6' }}>
                    <td style={{ padding: '12px', fontWeight: 'bold' }}>{data.symbol}</td>
                    <td style={{ padding: '12px', textAlign: 'right', fontFamily: 'monospace' }}>
                      ${formatPrice(data.last)}
                    </td>
                    <td style={{
                      padding: '12px',
                      textAlign: 'right',
                      color: (data.changePercent || 0) >= 0 ? '#28a745' : '#dc3545',
                      fontWeight: 'bold'
                    }}>
                      {(data.changePercent || 0) >= 0 ? '+' : ''}{(data.changePercent || 0).toFixed(2)}%
                    </td>
                    <td style={{ padding: '12px', textAlign: 'right', fontFamily: 'monospace' }}>
                      {formatVolume(data.volume)}
                    </td>
                    <td style={{ padding: '12px', textAlign: 'right', fontSize: '12px', color: '#666' }}>
                      {new Date(data.timestamp).toLocaleTimeString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {Object.keys(marketData).length === 0 && (
              <div style={{ padding: '40px', textAlign: 'center', color: '#666' }}>
                <div style={{ fontSize: '24px', marginBottom: '10px' }}>⏳</div>
                <div>正在加载市场数据...</div>
              </div>
            )}
          </div>
        </div>

        {/* WebSocket消息日志 */}
        <div>
          <h2 style={{ color: '#333', marginBottom: '15px' }}>📡 实时消息日志</h2>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            padding: '15px',
            height: '300px',
            overflow: 'auto'
          }}>
            {messages.map((message, index) => (
              <div key={index} style={{
                marginBottom: '8px',
                padding: '8px',
                backgroundColor: '#f8f9fa',
                borderRadius: '4px',
                fontSize: '12px',
                fontFamily: 'monospace'
              }}>
                <div style={{ color: '#666', marginBottom: '4px' }}>
                  {new Date(message.timestamp).toLocaleTimeString()} - {message.type}
                </div>
                <div style={{ color: '#333' }}>
                  {JSON.stringify(message, null, 2)}
                </div>
              </div>
            ))}

            {messages.length === 0 && (
              <div style={{ textAlign: 'center', color: '#666', paddingTop: '50px' }}>
                <div style={{ fontSize: '24px', marginBottom: '10px' }}>📝</div>
                <div>等待WebSocket消息...</div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CSS动画 */}
      <style>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}

// 其他页面组件
function MarketData() {
  return (
    <div style={{ padding: '20px' }}>
      <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#333', marginBottom: '20px' }}>
        📈 市场数据
      </h1>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        padding: '40px',
        textAlign: 'center'
      }}>
        <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
        <h3 style={{ fontSize: '18px', fontWeight: '500', color: '#333', margin: '0 0 8px' }}>
          市场数据功能
        </h3>
        <p style={{ color: '#666' }}>
          详细的市场数据分析功能正在开发中...
        </p>
      </div>
    </div>
  )
}

function Exchanges() {
  return (
    <div style={{ padding: '20px' }}>
      <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#333', marginBottom: '20px' }}>
        🏦 交易所管理
      </h1>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        padding: '40px',
        textAlign: 'center'
      }}>
        <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔗</div>
        <h3 style={{ fontSize: '18px', fontWeight: '500', color: '#333', margin: '0 0 8px' }}>
          交易所管理功能
        </h3>
        <p style={{ color: '#666' }}>
          交易所配置和管理功能正在开发中...
        </p>
      </div>
    </div>
  )
}

function Settings() {
  return (
    <div style={{ padding: '20px' }}>
      <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#333', marginBottom: '20px' }}>
        ⚙️ 系统设置
      </h1>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        padding: '40px',
        textAlign: 'center'
      }}>
        <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔧</div>
        <h3 style={{ fontSize: '18px', fontWeight: '500', color: '#333', margin: '0 0 8px' }}>
          系统设置功能
        </h3>
        <p style={{ color: '#666' }}>
          用户设置和系统配置功能正在开发中...
        </p>
      </div>
    </div>
  )
}

// 主应用组件
function App() {
  const { isAuthenticated, login, logout } = useAuth()

  return (
    <div style={{ fontFamily: 'Arial, sans-serif', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {isAuthenticated ? (
        <>
          <Navigation onLogout={logout} />
          <Routes>
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/market-data" element={<MarketData />} />
            <Route path="/strategies" element={<Strategies />} />
            <Route path="/exchanges" element={<Exchanges />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </>
      ) : (
        <Routes>
          <Route path="/login" element={<LoginForm />} />
          <Route path="/register" element={<RegisterForm />} />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      )}
    </div>
  )
}

export default App
