import { useState, useEffect } from 'react'

export interface Strategy {
  id: string
  name: string
  description?: string
  type: 'ARBITRAGE' | 'TREND' | 'MARKET_MAKER' | 'HIGH_FREQUENCY' | 'AI' | 'GRID' | 'DCA' | 'CUSTOM'
  status: 'RUNNING' | 'STOPPED' | 'PAUSED' | 'ERROR' | 'BACKTESTING'
  exchanges: string[]
  symbols: string[]
  parameters: Record<string, any>
  totalReturn: number
  dailyReturn: number
  maxDrawdown: number
  sharpeRatio: number
  winRate: number
  totalTrades: number
  maxPosition?: number
  stopLoss?: number
  takeProfit?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export function Strategies() {
  const [strategies, setStrategies] = useState<Strategy[]>([])
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [createError, setCreateError] = useState<string | null>(null)
  const [filter, setFilter] = useState<{
    status?: string
    type?: string
  }>({})

  // 策略创建表单数据
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'TREND' as Strategy['type'],
    language: 'typescript' as 'typescript' | 'python',
    code: '',
    exchanges: ['binance'],
    symbols: ['BTC/USDT'],
    parameters: {},
    enabled: false,
    riskLimits: {
      maxPositionSize: 1000,
      maxDailyLoss: 100,
      maxDrawdown: 0.1
    }
  })

  // 从API获取真实策略数据
  const fetchStrategies = async () => {
    try {
      setIsLoading(true)

      // 调用真实的API获取策略数据
      const token = localStorage.getItem('accessToken')
      if (!token) {
        setStrategies([])
        setIsLoading(false)
        return
      }

      const response = await fetch('/api/v1/strategies', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setStrategies(data.strategies || [])
      } else {
        console.error('Failed to fetch strategies:', response.statusText)
        setStrategies([])
      }

      setIsLoading(false)
    } catch (error) {
      console.error('Failed to fetch strategies:', error)
      setStrategies([])
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchStrategies()
  }, [])

  const filteredStrategies = strategies.filter(strategy => {
    if (filter.status && strategy.status !== filter.status) return false
    if (filter.type && strategy.type !== filter.type) return false
    return true
  })

  // 处理表单输入变化
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    // 清除错误信息
    if (createError) {
      setCreateError(null)
    }
  }

  // 创建策略
  const handleCreateStrategy = async () => {
    try {
      setIsCreating(true)
      setCreateError(null)

      // 验证必填字段
      if (!formData.name.trim()) {
        throw new Error('策略名称不能为空')
      }
      if (!formData.code.trim()) {
        throw new Error('策略代码不能为空')
      }

      const token = localStorage.getItem('accessToken')
      if (!token) {
        throw new Error('请先登录')
      }

      // 准备API请求数据，映射前端类型到后端期望的格式
      const typeMapping: Record<string, string> = {
        'ARBITRAGE': 'arbitrage',
        'TREND': 'trend',
        'MARKET_MAKER': 'market_maker',
        'HIGH_FREQUENCY': 'high_frequency',
        'AI': 'ai_ml',
        'GRID': 'trend' // 暂时映射为trend，后续可以扩展
      }

      const requestData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        type: typeMapping[formData.type] || 'trend',
        language: formData.language,
        code: formData.code,
        exchanges: formData.exchanges,
        symbols: formData.symbols,
        parameters: formData.parameters,
        enabled: formData.enabled,
        riskLimits: formData.riskLimits
      }

      const response = await fetch('/api/v1/strategies', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '创建策略失败')
      }

      const result = await response.json()

      // 重新获取策略列表
      await fetchStrategies()

      // 关闭模态框并重置表单
      setShowCreateModal(false)
      setFormData({
        name: '',
        description: '',
        type: 'TREND',
        language: 'typescript',
        code: '',
        exchanges: ['binance'],
        symbols: ['BTC/USDT'],
        parameters: {},
        enabled: false,
        riskLimits: {
          maxPositionSize: 1000,
          maxDailyLoss: 100,
          maxDrawdown: 0.1
        }
      })

    } catch (error) {
      setCreateError(error instanceof Error ? error.message : '创建策略失败')
    } finally {
      setIsCreating(false)
    }
  }

  const handleStrategyAction = (strategyId: string, action: 'start' | 'stop' | 'pause' | 'delete') => {
    setStrategies(prev => prev.map(strategy => {
      if (strategy.id === strategyId) {
        switch (action) {
          case 'start':
            return { ...strategy, status: 'RUNNING' as const }
          case 'stop':
            return { ...strategy, status: 'STOPPED' as const }
          case 'pause':
            return { ...strategy, status: 'PAUSED' as const }
          case 'delete':
            return { ...strategy, isActive: false }
          default:
            return strategy
        }
      }
      return strategy
    }).filter(strategy => strategy.isActive))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'RUNNING': return '#10b981'
      case 'STOPPED': return '#6b7280'
      case 'PAUSED': return '#f59e0b'
      case 'ERROR': return '#ef4444'
      case 'BACKTESTING': return '#8b5cf6'
      default: return '#6b7280'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'RUNNING': return '运行中'
      case 'STOPPED': return '已停止'
      case 'PAUSED': return '已暂停'
      case 'ERROR': return '错误'
      case 'BACKTESTING': return '回测中'
      default: return '未知'
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case 'ARBITRAGE': return '套利'
      case 'TREND': return '趋势'
      case 'MARKET_MAKER': return '做市'
      case 'HIGH_FREQUENCY': return '高频'
      case 'AI': return 'AI策略'
      case 'GRID': return '网格'
      case 'DCA': return '定投'
      default: return '自定义'
    }
  }

  const formatNumber = (num: number, decimals: number = 2) => {
    return num.toFixed(decimals)
  }

  const formatPercentage = (num: number) => {
    return `${(num * 100).toFixed(2)}%`
  }

  return (
    <div className="p-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            策略管理
          </h1>
          <p className="text-gray-600">
            创建、管理和监控您的量化交易策略
          </p>
        </div>

        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium flex items-center gap-2 transition-colors"
        >
          <span className="text-lg">+</span>
          创建策略
        </button>
      </div>

      {/* 筛选器 */}
      <div className="flex items-center gap-6 mb-6">
        <div>
          <label className="text-sm font-medium text-gray-700 mr-2">
            状态:
          </label>
          <select
            value={filter.status || ''}
            onChange={(e) => setFilter(prev => ({ ...prev, status: e.target.value || undefined }))}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部</option>
            <option value="RUNNING">运行中</option>
            <option value="STOPPED">已停止</option>
            <option value="PAUSED">已暂停</option>
            <option value="ERROR">错误</option>
          </select>
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700 mr-2">
            类型:
          </label>
          <select
            value={filter.type || ''}
            onChange={(e) => setFilter(prev => ({ ...prev, type: e.target.value || undefined }))}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部</option>
            <option value="ARBITRAGE">套利</option>
            <option value="TREND">趋势</option>
            <option value="MARKET_MAKER">做市</option>
            <option value="GRID">网格</option>
            <option value="AI">AI策略</option>
          </select>
        </div>

        <div className="ml-auto text-gray-600 text-sm">
          共 {filteredStrategies.length} 个策略
        </div>
      </div>

      {/* 策略列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {isLoading ? (
          <div className="p-16 text-center">
            <div className="w-10 h-10 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4" />
            <p className="text-gray-600">加载策略列表...</p>
          </div>
        ) : filteredStrategies.length === 0 ? (
          <div className="p-16 text-center">
            <div className="text-5xl mb-4">📊</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              暂无策略
            </h3>
            <p className="text-gray-600 mb-6">
              开始创建您的第一个量化交易策略
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors"
            >
              创建策略
            </button>
          </div>
        ) : (
          <div>
            {/* 表头 */}
            <div className="grid grid-cols-7 gap-4 px-6 py-4 bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-500 uppercase tracking-wider">
              <div>策略名称</div>
              <div>类型</div>
              <div>状态</div>
              <div>总收益率</div>
              <div>日收益率</div>
              <div>交易次数</div>
              <div>操作</div>
            </div>

            {/* 策略行 */}
            {filteredStrategies.map((strategy) => (
              <div
                key={strategy.id}
                className="grid grid-cols-7 gap-4 px-6 py-4 border-b border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => setSelectedStrategy(strategy)}
              >
                {/* 策略名称和描述 */}
                <div>
                  <div className="font-medium text-gray-900 mb-1">
                    {strategy.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {strategy.description || '暂无描述'}
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    {strategy.symbols.join(', ')}
                  </div>
                </div>

                {/* 策略类型 */}
                <div>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    {getTypeText(strategy.type)}
                  </span>
                </div>

                {/* 状态 */}
                <div>
                  <span
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white"
                    style={{ backgroundColor: getStatusColor(strategy.status) }}
                  >
                    {getStatusText(strategy.status)}
                  </span>
                </div>

                {/* 总收益率 */}
                <div className={`font-medium ${strategy.totalReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatPercentage(strategy.totalReturn)}
                </div>

                {/* 日收益率 */}
                <div className={`font-medium ${strategy.dailyReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatPercentage(strategy.dailyReturn)}
                </div>

                {/* 交易次数 */}
                <div className="text-gray-900">
                  {strategy.totalTrades}
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center gap-2">
                  {strategy.status === 'STOPPED' ? (
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleStrategyAction(strategy.id, 'start')
                      }}
                      className="text-green-600 hover:text-green-800 text-sm font-medium"
                    >
                      启动
                    </button>
                  ) : (
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleStrategyAction(strategy.id, 'stop')
                      }}
                      className="text-red-600 hover:text-red-800 text-sm font-medium"
                    >
                      停止
                    </button>
                  )}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleStrategyAction(strategy.id, 'delete')
                    }}
                    className="text-gray-400 hover:text-red-600 text-sm font-medium"
                  >
                    删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 创建策略模态框 */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-auto">
            <h2 className="text-xl font-bold mb-6">
              创建新策略
            </h2>

            {/* 错误提示 */}
            {createError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4 text-red-700 text-sm">
                {createError}
              </div>
            )}

            {/* 策略基本信息 */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3 text-gray-900">
                基本信息
              </h3>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  策略名称 *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleFormChange('name', e.target.value)}
                  placeholder="请输入策略名称"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  策略描述
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleFormChange('description', e.target.value)}
                  placeholder="请输入策略描述"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical"
                />
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    策略类型 *
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => handleFormChange('type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="ARBITRAGE">套利策略</option>
                    <option value="TREND">趋势策略</option>
                    <option value="MARKET_MAKER">做市策略</option>
                    <option value="HIGH_FREQUENCY">高频策略</option>
                    <option value="AI">AI策略</option>
                    <option value="GRID">网格策略</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    编程语言 *
                  </label>
                  <select
                    value={formData.language}
                    onChange={(e) => handleFormChange('language', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="typescript">TypeScript</option>
                    <option value="python">Python</option>
                  </select>
                </div>
              </div>
            </div>

            {/* 策略代码 */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3 text-gray-900">
                策略代码
              </h3>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  策略代码 *
                </label>
                <textarea
                  value={formData.code}
                  onChange={(e) => handleFormChange('code', e.target.value)}
                  placeholder={formData.language === 'typescript'
                    ? `// TypeScript 策略代码示例
export function strategy(context: StrategyContext) {
  const { marketData, indicators, portfolio } = context

  // 在这里编写您的策略逻辑
  const signal = {
    action: 'HOLD',
    quantity: 0,
    price: marketData.close,
    confidence: 0.5,
    reason: '示例策略'
  }

  return { signals: [signal] }
}`
                    : `# Python 策略代码示例
def strategy(context):
    market_data = context['market_data']
    indicators = context['indicators']
    portfolio = context['portfolio']

    # 在这里编写您的策略逻辑
    signal = {
        'action': 'HOLD',
        'quantity': 0,
        'price': market_data['close'],
        'confidence': 0.5,
        'reason': '示例策略'
    }

    return {'signals': [signal]}
`}
                  rows={12}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm font-mono focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical"
                />
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowCreateModal(false)}
                disabled={isCreating}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                取消
              </button>
              <button
                onClick={handleCreateStrategy}
                disabled={isCreating || !formData.name.trim() || !formData.code.trim()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isCreating && (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                )}
                {isCreating ? '创建中...' : '创建策略'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
