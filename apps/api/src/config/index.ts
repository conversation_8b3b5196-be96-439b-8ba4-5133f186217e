import { z } from 'zod'

// 环境变量验证schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3001'),
  HOST: z.string().default('0.0.0.0'),

  // 数据库配置
  DATABASE_URL: z.string().default('postgresql://postgres:password@localhost:5432/sfquant'),

  // Redis配置
  REDIS_URL: z.string().default('redis://localhost:6379'),

  // JWT配置
  JWT_SECRET: z.string().default('your-super-secret-jwt-key-change-in-production'),
  JWT_EXPIRES_IN: z.string().default('7d'),

  // CORS配置
  CORS_ORIGINS: z.string().default('http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:3003'),

  // 限流配置
  RATE_LIMIT_MAX: z.string().transform(Number).default('100'),
  RATE_LIMIT_WINDOW: z.string().default('1 minute'),

  // 日志级别
  LOG_LEVEL: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),

  // 加密配置
  ENCRYPTION_KEY: z.string().default('your-32-character-encryption-key!!'),

  // 外部API配置
  COINGECKO_API_KEY: z.string().optional(),
  COINMARKETCAP_API_KEY: z.string().optional(),

  // WebSocket配置
  WS_HEARTBEAT_INTERVAL: z.string().transform(Number).default('30000'), // 30秒
  WS_HEARTBEAT_TIMEOUT: z.string().transform(Number).default('60000'), // 60秒
  WS_MAX_CONNECTIONS: z.string().transform(Number).default('1000'),

  // 策略执行配置
  STRATEGY_TIMEOUT: z.string().transform(Number).default('30000'), // 30秒
  MAX_CONCURRENT_STRATEGIES: z.string().transform(Number).default('10'),

  // 市场数据配置
  MARKET_DATA_CACHE_TTL: z.string().transform(Number).default('60'), // 60秒
  PRICE_UPDATE_INTERVAL: z.string().transform(Number).default('1000'), // 1秒
})

// 解析环境变量
const env = envSchema.parse(process.env)

// 导出配置
export const config = {
  // 基础配置
  NODE_ENV: env.NODE_ENV,
  PORT: env.PORT,
  HOST: env.HOST,

  // 数据库
  DATABASE_URL: env.DATABASE_URL,

  // Redis
  REDIS_URL: env.REDIS_URL,

  // JWT
  JWT_SECRET: env.JWT_SECRET,
  JWT_EXPIRES_IN: env.JWT_EXPIRES_IN,

  // CORS
  CORS_ORIGINS: env.CORS_ORIGINS.split(','),

  // 限流
  RATE_LIMIT_MAX: env.RATE_LIMIT_MAX,
  RATE_LIMIT_WINDOW: env.RATE_LIMIT_WINDOW,

  // 日志
  LOG_LEVEL: env.LOG_LEVEL,

  // 加密
  ENCRYPTION_KEY: env.ENCRYPTION_KEY,

  // 外部API
  COINGECKO_API_KEY: env.COINGECKO_API_KEY,
  COINMARKETCAP_API_KEY: env.COINMARKETCAP_API_KEY,

  // WebSocket
  WS_HEARTBEAT_INTERVAL: env.WS_HEARTBEAT_INTERVAL,
  WS_HEARTBEAT_TIMEOUT: env.WS_HEARTBEAT_TIMEOUT,
  WS_MAX_CONNECTIONS: env.WS_MAX_CONNECTIONS,

  // 策略执行
  STRATEGY_TIMEOUT: env.STRATEGY_TIMEOUT,
  MAX_CONCURRENT_STRATEGIES: env.MAX_CONCURRENT_STRATEGIES,

  // 市场数据
  MARKET_DATA_CACHE_TTL: env.MARKET_DATA_CACHE_TTL,
  PRICE_UPDATE_INTERVAL: env.PRICE_UPDATE_INTERVAL,

  // 支持的交易所
  SUPPORTED_CEX: [
    'binance',
    'okx',
    'bybit',
    'coinbase',
    'kraken',
    'huobi',
    'kucoin'
  ],

  SUPPORTED_DEX: [
    'uniswap-v2',
    'uniswap-v3',
    'sushiswap',
    'pancakeswap',
    'curve',
    'balancer'
  ],

  // 支持的区块链
  SUPPORTED_CHAINS: [
    { id: 1, name: 'Ethereum', rpc: 'https://eth.llamarpc.com' },
    { id: 56, name: 'BSC', rpc: 'https://bsc-dataseed.binance.org' },
    { id: 137, name: 'Polygon', rpc: 'https://polygon-rpc.com' },
    { id: 42161, name: 'Arbitrum', rpc: 'https://arb1.arbitrum.io/rpc' },
    { id: 10, name: 'Optimism', rpc: 'https://mainnet.optimism.io' }
  ],

  // 默认策略参数
  DEFAULT_STRATEGY_PARAMS: {
    maxPositionSize: 1000, // USDT
    maxDailyLoss: 100, // USDT
    maxDrawdown: 0.1, // 10%
    slippageTolerance: 0.005, // 0.5%
    gasLimit: 200000,
    timeout: 30000 // 30秒
  }
} as const

// 类型导出
export type Config = typeof config
